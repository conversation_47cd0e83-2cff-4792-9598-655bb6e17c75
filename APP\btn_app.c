/* Licence
* Company: MCUSTUDIO
* Auther: Ah<PERSON>pnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"


extern uint8_t ucLed[6];
char bt1=0;


typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_6,
    USER_BUTTON_MAX,
} user_button_t;

static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_6, &defaul_ebtn_param),
};


uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !KEY1_READ;
    case USER_BUTTON_1:
        return !KEY2_READ;
    case USER_BUTTON_2:
        return !KEY3_READ;
    case USER_BUTTON_3:
        return !KEY4_READ;
    case USER_BUTTON_4:
        return !KEY5_READ;
    case USER_BUTTON_5:
        return !KEY6_READ;
    case USER_BUTTON_6:
        return !KEYW_READ;
    default:
        return 0;
    }
}






void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if (evt == EBTN_EVT_ONCLICK)
    {
				char str2[10];
		char command3[50]="cycle switch to ";
		char command4[20]="s (key press)";
				char str3[10];
		char command5[50]="cycle switch to ";
		char command6[20]="s (key press)";
				char str4[10];
		char command7[50]="cycle switch to ";
		char command8[20]="s (key press)";

			
        switch (btn->key_id)
        {
        case USER_BUTTON_0:
					bt1^=1;
				if(bt1==0)
				{
					usart_flag=22;
					my_printf(DEBUG_USART,"Periodic Sampling STOP\r\n");

				write_log_to_file("sample stop (key press)");
				}

				else if(bt1==1)
				{	usart_flag=20;
								char str1[10];
		sprintf(str1, "%d", limit_time/1000);
		char command1[50]="sample start - cycle ";
		char command2[20]="s (key press)";
		strcat(command1, str1);
		strcat(command1, command2);
		write_log_to_file(command1);			
				}
						
            break;
        case USER_BUTTON_1:
            limit_time=5000;
		sprintf(str2, "%d", limit_time/1000);
		strcat(command3, str2);
		strcat(command3, command4);
		write_log_to_file(command3);			

						btn_flag=1;
            break;
        case USER_BUTTON_2:
            limit_time=15000;
		sprintf(str3, "%d", limit_time/1000);
		strcat(command5, str3);
		strcat(command5, command6);
		write_log_to_file(command5);			
				
						btn_flag=1;
            break;
        case USER_BUTTON_3:
            limit_time=20000;
		sprintf(str4, "%d", limit_time/1000);
		strcat(command7, str4);
		strcat(command7, command8);
		write_log_to_file(command7);			
		
				
						btn_flag=1;
            break;
        case USER_BUTTON_4:
            break;
        case USER_BUTTON_5:
            break;
        case USER_BUTTON_6:
            LED6_TOGGLE;
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

}

void btn_task(void)
{
    ebtn_process(get_system_ms());
}
