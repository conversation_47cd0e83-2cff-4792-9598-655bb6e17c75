/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"
#include <stdlib.h>
#include <string.h>
#include "rtc_app.h"
#include "gd25qxx.h"


char usart_flag=1;  // 初始化为1，跳过自动打印逻辑
	float ratio_value;
	float limit_value;
char hide_flag=0;


__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};




int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    uint16_t i;  // 使用局部变量而不是全局变量

    // 初始化可变参数列表
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    for(i = 0; i < len; i++){
        usart_data_transmit(usart_periph, buffer[i]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }

    return len;
}







void sys_flag()
{
	if(strncmp((const char*)uart_dma_buffer, "RTC Config", 10) == 0)
	{my_printf(DEBUG_USART,"Input Datetime \r\n");
		usart_flag=2;
		}
	if(strncmp((const char*)uart_dma_buffer, "test", 4) == 0)
	{
	usart_flag=4;
	
	}
	if(strncmp((const char*)uart_dma_buffer, "conf", 4) == 0)
	usart_flag=6;
		
  if(strncmp((const char*)uart_dma_buffer, "ratio", 5) == 0)
		usart_flag=8;
	if(strncmp((const char*)uart_dma_buffer, "limit", 5) == 0)
		usart_flag=12;
	if(strncmp((const char*)uart_dma_buffer, "config save", 11) == 0)
		usart_flag=16;
	if(strncmp((const char*)uart_dma_buffer, "config read", 11) == 0)
		usart_flag=18;
	if(strncmp((const char*)uart_dma_buffer, "start", 5)==0)
	{
	  usart_flag=20;
		
		char str[10];
		sprintf(str, "%d", limit_time/1000);
		char command1[50]="sample start - cycle ";
		char command2[20]="s (command)";
		strcat(command1, str);
		strcat(command1, command2);
		write_log_to_file(command1);
		
	}
		if(strncmp((const char*)uart_dma_buffer, "stop", 4) == 0)
		{
	usart_flag=22;
			my_printf(DEBUG_USART,"Periodic Sampling STOP\r\n");
		write_log_to_file("sample stop (command)");

		}
        // 尝试从命令中提取浮点数hide
	if(strncmp((const char*)uart_dma_buffer, "hide", 4) == 0)
	{
	hide_flag=1;
		write_log_to_file("hide data");
	}
	if(strncmp((const char*)uart_dma_buffer, "unhide", 6) == 0)
	{
		write_log_to_file("unhide data");
	hide_flag=0;
	}
}



void uart_task(void)
{
    // 移除自动打印逻辑，改为通过命令触发
    // 原来的自动打印逻辑已被移除，现在只响应串口命令

    if(!rx_flag) return;
    sys_flag();
    if(usart_flag==2)
    {
        
        
        // 检查输入的是否是日期时间格式
        if(strlen((const char*)uart_dma_buffer) >= 19) {
            if(parse_and_set_datetime((const char*)uart_dma_buffer)) {
                my_printf(DEBUG_USART, "RTC time set successfully\r\n");
                // 创建一个干净的时间字符串来打印（只取前19个字符）
                char clean_time_str[20];
                strncpy(clean_time_str, (const char*)uart_dma_buffer, 19);
                clean_time_str[19] = '\0';  // 确保字符串结束
                my_printf(DEBUG_USART, "RTC time set %s\r\n", clean_time_str);

                // 将日志写入移到串口输出之后，避免冲突
                write_log_to_file("rtc config");
                usart_flag = 3; // 设置成功后更改标志位
            } else {
                my_printf(DEBUG_USART, "Failed to set RTC time. Format: YYYY-MM-DD HH:MM:SS\r\n");
            }
        }
    }
    if(strncmp((const char*)uart_dma_buffer, "RTC now", 7) == 0)
		{
			print_time();
		}
		if(usart_flag==4)
		{
		my_printf(DEBUG_USART,"======system selftest======\r\n");
		sys_all_test();
		my_printf(DEBUG_USART,"======system selftest======\r\n");
		// 将日志写入移到串口输出之后，避免冲突
		write_log_to_file("system hardware test");
			usart_flag=5;
		}
		
		
		if(usart_flag==9)
		{
			//my_printf(DEBUG_USART,"Input value(0~100):\r\n");
					sscanf((const char*)uart_dma_buffer, "%f", &ratio_value);
		//my_printf(DEBUG_USART,"====77777777%f====\r\n",ratio_value);	

		usart_flag=10;
		
		}
		
				if(usart_flag==13)
		{
		  sscanf((const char*)uart_dma_buffer, "%f", &limit_value);
			//		my_printf(DEBUG_USART,"====666666%f====\r\n",limit_value);
				//my_printf(DEBUG_USART,"Input value(0~500):\r\n");

		usart_flag=14;
		}

		
		
		if(usart_flag==8)
		{		
			write_log_to_file("ratio config");
			my_printf(DEBUG_USART,"Ratio = %1.f\r\n",g_ratio_value);
			my_printf(DEBUG_USART,"Input value(0~100):\r\n");
		//my_printf(DEBUG_USART,"====66666666666666====\r\n");	
		usart_flag=9;
		}
		
		if(usart_flag==12)
		{		
			write_log_to_file("limit config");
			my_printf(DEBUG_USART,"limit = %1.f\r\n",g_limit_value);
			my_printf(DEBUG_USART,"Input value(0~500):\r\n");
			
		usart_flag=13;
		}
		
		
		
		
		
		
		
		
		
	
    rx_flag = 0;
    memset(uart_dma_buffer, 0, 256);
}










