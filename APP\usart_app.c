/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"
#include <stdlib.h>
#include <string.h>
#include "rtc_app.h"
#include "gd25qxx.h"


char usart_flag=0;
	float ratio_value;
	float limit_value;
char hide_flag=0;


__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};




int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // ��ʼ���ɱ�����б�
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }
    
    return len;
}







void sys_flag()
{
	if(strncmp((const char*)uart_dma_buffer, "RTC Config", 10) == 0)
	{my_printf(DEBUG_USART,"Input Datetime \r\n");
		usart_flag=2;
		}
	if(strncmp((const char*)uart_dma_buffer, "test", 4) == 0)
	{
	usart_flag=4;
	
	}
	if(strncmp((const char*)uart_dma_buffer, "conf", 4) == 0)
	usart_flag=6;
		
  if(strncmp((const char*)uart_dma_buffer, "ratio", 5) == 0)
		usart_flag=8;
	if(strncmp((const char*)uart_dma_buffer, "limit", 5) == 0)
		usart_flag=12;
	if(strncmp((const char*)uart_dma_buffer, "config save", 11) == 0)
		usart_flag=16;
	if(strncmp((const char*)uart_dma_buffer, "config read", 11) == 0)
		usart_flag=18;
	if(strncmp((const char*)uart_dma_buffer, "start", 5)==0)
	{
	  usart_flag=20;
		
		char str[10];
		sprintf(str, "%d", limit_time/1000);
		char command1[50]="sample start - cycle ";
		char command2[20]="s (command)";
		strcat(command1, str);
		strcat(command1, command2);
		write_log_to_file(command1);
		
	}
		if(strncmp((const char*)uart_dma_buffer, "stop", 4) == 0)
		{
	usart_flag=22;
			my_printf(DEBUG_USART,"Periodic Sampling STOP\r\n");
		write_log_to_file("sample stop (command)");

		}
        // 尝试从命令中提取浮点数hide
	if(strncmp((const char*)uart_dma_buffer, "hide", 4) == 0)
	{
	hide_flag=1;
		write_log_to_file("hide data");
	}
	if(strncmp((const char*)uart_dma_buffer, "unhide", 6) == 0)
	{
		write_log_to_file("unhide data");
	hide_flag=0;
	}
}



void uart_task(void)
{
    
    if(usart_flag==0)
    {
        my_printf(DEBUG_USART,"====system init====\r\n");
					//my_write_flash();
				my_reaf_flash();
				
        my_printf(DEBUG_USART,"====system ready====\r\n");
        usart_flag=1;
    }

    if(!rx_flag) return;
    sys_flag();
    if(usart_flag==2)
    {
        
        
        // 检查输入的是否是日期时间格式
        if(strlen((const char*)uart_dma_buffer) >= 19) {
					
					write_log_to_file("rtc config");
            if(parse_and_set_datetime((const char*)uart_dma_buffer)) {
                my_printf(DEBUG_USART, "RTC time set successfully\r\n");
                // 获取并打印当前RTC时间
							my_printf(DEBUG_USART, "RTC time set %s\r\n",uart_dma_buffer);
                usart_flag = 3; // 设置成功后更改标志位
            } else {
                my_printf(DEBUG_USART, "Failed to set RTC time. Format: YYYY-MM-DD HH:MM:SS\r\n");
            }
        }
    }
    if(strncmp((const char*)uart_dma_buffer, "RTC now", 7) == 0)
		{
			print_time();
		}
		if(usart_flag==4)
		{
		my_printf(DEBUG_USART,"======system selftest======\r\n");
		write_log_to_file("system hardware test");
		sys_all_test();
		my_printf(DEBUG_USART,"======system selftest======\r\n");	
			usart_flag=5;
		}
		
		
		if(usart_flag==9)
		{
			//my_printf(DEBUG_USART,"Input value(0~100):\r\n");
					sscanf((const char*)uart_dma_buffer, "%f", &ratio_value);
		//my_printf(DEBUG_USART,"====77777777%f====\r\n",ratio_value);	

		usart_flag=10;
		
		}
		
				if(usart_flag==13)
		{
		  sscanf((const char*)uart_dma_buffer, "%f", &limit_value);
			//		my_printf(DEBUG_USART,"====666666%f====\r\n",limit_value);
				//my_printf(DEBUG_USART,"Input value(0~500):\r\n");

		usart_flag=14;
		}

		
		
		if(usart_flag==8)
		{		
			write_log_to_file("ratio config");
			my_printf(DEBUG_USART,"Ratio = %1.f\r\n",g_ratio_value);
			my_printf(DEBUG_USART,"Input value(0~100):\r\n");
		//my_printf(DEBUG_USART,"====66666666666666====\r\n");	
		usart_flag=9;
		}
		
		if(usart_flag==12)
		{		
			write_log_to_file("limit config");
			my_printf(DEBUG_USART,"limit = %1.f\r\n",g_limit_value);
			my_printf(DEBUG_USART,"Input value(0~500):\r\n");
			
		usart_flag=13;
		}
		
		
		
		
		
		
		
		
		
	
    rx_flag = 0;
    memset(uart_dma_buffer, 0, 256);
}










