File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,16.215683%,12327,96,11776,551,0,96
ff.o,9.833068%,7475,518,7456,13,6,512
sdio_sdcard.o,9.431852%,7170,68,7134,0,36,32
adc_app.o,6.635183%,5044,1869,4960,51,33,1836
system.o,5.884055%,4473,625,4320,140,13,612
oled.o,5.230271%,3976,22,1242,2712,22,0
btod.o,2.830871%,2152,0,2152,0,0,0
ebtn.o,2.723003%,2070,60,2070,0,0,60
mcu_cmic_gd32f470vet6.o,2.183665%,1660,592,1640,0,20,572
fz_wm.l,2.002131%,1522,0,1506,16,0,0
usart_app.o,1.906102%,1449,529,1360,72,17,512
gd32f4xx_dma.o,1.752194%,1332,0,1332,0,0,0
scanf_fp.o,1.673266%,1272,0,1272,0,0,0
btn_app.o,1.649588%,1254,200,750,304,200,0
_printf_fp_dec.o,1.386495%,1054,0,1054,0,0,0
gd25qxx.o,1.302306%,990,0,990,0,0,0
perf_counter.o,1.270735%,966,80,882,4,80,0
_scanf.o,1.162867%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.136558%,864,0,864,0,0,0
_printf_fp_hex.o,1.054999%,802,0,764,38,0,0
scanf_hexfp.o,1.052369%,800,0,800,0,0,0
m_wm.l,0.981334%,746,0,746,0,0,0
system_gd32f4xx.o,0.918191%,698,4,694,0,4,0
fatfs_unicode.o,0.907668%,690,0,474,216,0,0
gd32f4xx_adc.o,0.860311%,654,0,654,0,0,0
gd32f4xx_usart.o,0.847157%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.826109%,628,0,628,0,0,0
gd32f4xx_timer.o,0.773491%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.652468%,496,0,496,0,0,0
startup_gd32f450_470.o,0.647207%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.636683%,484,0,484,0,0,0
__printf_flags_ss_wp.o,0.538023%,409,0,392,17,0,0
bigflt0.o,0.494613%,376,0,228,148,0,0
led_app.o,0.451203%,343,11,332,0,11,0
dmul.o,0.447257%,340,0,340,0,0,0
lc_ctype_c.o,0.415686%,316,0,44,272,0,0
diskio.o,0.415686%,316,0,316,0,0,0
scanf_infnan.o,0.405162%,308,0,308,0,0,0
narrow.o,0.349913%,266,0,266,0,0,0
rtc_app.o,0.348597%,265,1,264,0,1,0
gd32f4xx_gpio.o,0.344651%,262,0,262,0,0,0
lludivv7m.o,0.313080%,238,0,238,0,0,0
ldexp.o,0.299925%,228,0,228,0,0,0
gd32f4xx_misc.o,0.284139%,216,0,216,0,0,0
gd32f4xx_dac.o,0.260461%,198,0,198,0,0,0
_printf_wctomb.o,0.257830%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.247307%,188,0,148,40,0,0
_printf_intcommon.o,0.234152%,178,0,178,0,0,0
scheduler.o,0.231521%,176,76,100,0,76,0
systick.o,0.220997%,168,4,164,0,4,0
gd32f4xx_it.o,0.220997%,168,0,168,0,0,0
dnaninf.o,0.205212%,156,0,156,0,0,0
perfc_port_default.o,0.202581%,154,0,154,0,0,0
strncmp.o,0.197319%,150,0,150,0,0,0
frexp.o,0.184164%,140,0,140,0,0,0
fnaninf.o,0.184164%,140,0,140,0,0,0
rt_memcpy_v6.o,0.181534%,138,0,138,0,0,0
lludiv10.o,0.181534%,138,0,138,0,0,0
strcmpv7m.o,0.168379%,128,0,128,0,0,0
_printf_fp_infnan.o,0.168379%,128,0,128,0,0,0
main.o,0.168379%,128,0,128,0,0,0
_printf_longlong_dec.o,0.163117%,124,0,124,0,0,0
dleqf.o,0.157855%,120,0,120,0,0,0
deqf.o,0.157855%,120,0,120,0,0,0
_printf_dec.o,0.157855%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.147332%,112,0,112,0,0,0
drleqf.o,0.142070%,108,0,108,0,0,0
oled_app.o,0.142070%,108,0,108,0,0,0
gd32f4xx_spi.o,0.136808%,104,0,104,0,0,0
retnan.o,0.131546%,100,0,100,0,0,0
rt_memcpy_w.o,0.131546%,100,0,100,0,0,0
d2f.o,0.128915%,98,0,98,0,0,0
scalbn.o,0.121022%,92,0,92,0,0,0
__dczerorl2.o,0.118391%,90,0,90,0,0,0
memcmp.o,0.115761%,88,0,88,0,0,0
f2d.o,0.113130%,86,0,86,0,0,0
strncpy.o,0.113130%,86,0,86,0,0,0
_printf_str.o,0.107868%,82,0,82,0,0,0
rt_memclr_w.o,0.102606%,78,0,78,0,0,0
_printf_pad.o,0.102606%,78,0,78,0,0,0
sys_stackheap_outer.o,0.097344%,74,0,74,0,0,0
sd_app.o,0.097344%,74,1436,74,0,0,1436
llsdiv.o,0.094713%,72,0,72,0,0,0
lc_numeric_c.o,0.094713%,72,0,44,28,0,0
rt_memclr.o,0.089451%,68,0,68,0,0,0
dunder.o,0.084189%,64,0,64,0,0,0
_wcrtomb.o,0.084189%,64,0,64,0,0,0
_sgetc.o,0.084189%,64,0,64,0,0,0
strlen.o,0.081559%,62,0,62,0,0,0
__0sscanf.o,0.078928%,60,0,60,0,0,0
vsnprintf.o,0.068404%,52,0,52,0,0,0
__scatter.o,0.068404%,52,0,52,0,0,0
fpclassify.o,0.063142%,48,0,48,0,0,0
trapv.o,0.063142%,48,0,48,0,0,0
_printf_char_common.o,0.063142%,48,0,48,0,0,0
scanf_char.o,0.057880%,44,0,44,0,0,0
_printf_wchar.o,0.057880%,44,0,44,0,0,0
_printf_char.o,0.057880%,44,0,44,0,0,0
__2sprintf.o,0.057880%,44,0,44,0,0,0
_printf_charcount.o,0.052618%,40,0,40,0,0,0
llshl.o,0.049988%,38,0,38,0,0,0
libinit2.o,0.049988%,38,0,38,0,0,0
strstr.o,0.047357%,36,0,36,0,0,0
init_aeabi.o,0.047357%,36,0,36,0,0,0
_printf_truncate.o,0.047357%,36,0,36,0,0,0
systick_wrapper_ual.o,0.042095%,32,0,32,0,0,0
_chval.o,0.036833%,28,0,28,0,0,0
__scatter_zi.o,0.036833%,28,0,28,0,0,0
dcmpi.o,0.031571%,24,0,24,0,0,0
strcat.o,0.031571%,24,0,24,0,0,0
_rserrno.o,0.028940%,22,0,22,0,0,0
gd32f4xx_pmu.o,0.026309%,20,0,20,0,0,0
isspace.o,0.023678%,18,0,18,0,0,0
exit.o,0.023678%,18,0,18,0,0,0
fpconst.o,0.021047%,16,0,0,16,0,0
dcheck1.o,0.021047%,16,0,16,0,0,0
rt_ctype_table.o,0.021047%,16,0,16,0,0,0
_snputc.o,0.021047%,16,0,16,0,0,0
__printf_wp.o,0.018416%,14,0,14,0,0,0
dretinf.o,0.015786%,12,0,12,0,0,0
sys_exit.o,0.015786%,12,0,12,0,0,0
__rtentry2.o,0.015786%,12,0,12,0,0,0
fretinf.o,0.013155%,10,0,10,0,0,0
fpinit.o,0.013155%,10,0,10,0,0,0
rtexit2.o,0.013155%,10,0,10,0,0,0
_sputc.o,0.013155%,10,0,10,0,0,0
_printf_ll.o,0.013155%,10,0,10,0,0,0
_printf_l.o,0.013155%,10,0,10,0,0,0
scanf2.o,0.010524%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010524%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010524%,8,0,8,0,0,0
libspace.o,0.010524%,8,96,8,0,0,96
__main.o,0.010524%,8,0,8,0,0,0
istatus.o,0.007893%,6,0,6,0,0,0
heapauxi.o,0.007893%,6,0,6,0,0,0
_printf_x.o,0.007893%,6,0,6,0,0,0
_printf_u.o,0.007893%,6,0,6,0,0,0
_printf_s.o,0.007893%,6,0,6,0,0,0
_printf_p.o,0.007893%,6,0,6,0,0,0
_printf_o.o,0.007893%,6,0,6,0,0,0
_printf_n.o,0.007893%,6,0,6,0,0,0
_printf_ls.o,0.007893%,6,0,6,0,0,0
_printf_llx.o,0.007893%,6,0,6,0,0,0
_printf_llu.o,0.007893%,6,0,6,0,0,0
_printf_llo.o,0.007893%,6,0,6,0,0,0
_printf_lli.o,0.007893%,6,0,6,0,0,0
_printf_lld.o,0.007893%,6,0,6,0,0,0
_printf_lc.o,0.007893%,6,0,6,0,0,0
_printf_i.o,0.007893%,6,0,6,0,0,0
_printf_g.o,0.007893%,6,0,6,0,0,0
_printf_f.o,0.007893%,6,0,6,0,0,0
_printf_e.o,0.007893%,6,0,6,0,0,0
_printf_d.o,0.007893%,6,0,6,0,0,0
_printf_c.o,0.007893%,6,0,6,0,0,0
_printf_a.o,0.007893%,6,0,6,0,0,0
__rtentry4.o,0.007893%,6,0,6,0,0,0
scanf1.o,0.005262%,4,0,4,0,0,0
printf2.o,0.005262%,4,0,4,0,0,0
printf1.o,0.005262%,4,0,4,0,0,0
_printf_percent_end.o,0.005262%,4,0,4,0,0,0
use_no_semi.o,0.002631%,2,0,2,0,0,0
rtexit.o,0.002631%,2,0,2,0,0,0
libshutdown2.o,0.002631%,2,0,2,0,0,0
libshutdown.o,0.002631%,2,0,2,0,0,0
libinit.o,0.002631%,2,0,2,0,0,0
